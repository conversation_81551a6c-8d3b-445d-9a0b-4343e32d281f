import React, { useEffect } from 'react';
import classNames from 'classnames';
import './index.less';
import { sendEvent } from '@/common/utils';
import styled from 'styled-components';
import { NewBusinessNewsType, PEER_SELECTOR_DATA_TYPES } from '../const';
import { useDataState } from '../store/useDataState';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import RedSelect from '../common/red-select';
import { queryPeersData } from '@/services';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';

interface IProps {
  fields: Array<{ key: string; title: string; children: any[]; [key: string]: any }>;
  hiddenFields: string[];
  onHiddenFieldsChange: (value, businessNewsType?: NewBusinessNewsType) => void;
  hiddenHistory: NewBusinessNewsType[];
  businessNewsType: NewBusinessNewsType;
  pid: string;
}

const Wrap = styled.div`
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  padding: 10px;
  color: rgba(0, 0, 0, 0.85);
  margin-top: 8px;
  /* max-height: 300px;
  overflow-y: auto; */
`;

function handleShopFields(
  list: any[],
  parentKey = '',
): Array<{ title: string; key: string; defaultHidden: boolean; allowPeer: boolean }> {
  const newFields = [];
  list.forEach((item) => {
    if (item.children || item.expandChildrenData) {
      newFields.push(...handleShopFields(item.children || item.expandChildrenData || [], item.key));
    } else {
      newFields.push({
        ...item,
        parentKey,
      });
    }
  });
  return newFields;
}

export default function WeeklyReportHiddenFields(props: IProps) {
  const { fields, onHiddenFieldsChange, hiddenFields, businessNewsType, hiddenHistory, pid } =
    props;
  const {
    fields: extFields,
    hiddenFields: extHiddenFields,
    onHiddenFieldChange,
    hidePeerMapKey,
    showPeerMapKey,
    handlePeerMapKeyChange,
  } = useDataState();
  const handleClick = (item) => {
    if (item.render) {
      return;
    }
    const { key: value, title } = item;
    const newList = [...hiddenFields];
    let checkable;
    if (hiddenFields.includes(value)) {
      newList.splice(newList.indexOf(value), 1);
      checkable = false;
      hidePeerMapKey(value);
    } else {
      newList.push(value);
      checkable = true;
      showPeerMapKey(value);
    }
    onHiddenFieldsChange(newList);
    sendEvent('HIDDEN_FIELDS', 'CLK', {
      c1: title,
      c2: checkable ? '隐藏字段' : '显示字段',
    });
    traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.字段切换'], {
      field: title,
      action: checkable ? '隐藏字段' : '显示字段',
      pid,
    });
  };
  useEffect(() => {
    const flattenFields = handleShopFields(fields);
    handlePeerMapKeyChange(flattenFields.filter((item) => item.allowPeer));
    if (!hiddenHistory.includes(businessNewsType)) {
      onHiddenFieldsChange(
        flattenFields.filter((item) => item.defaultHidden).map((item) => item.key),
        businessNewsType,
      );
    }
  }, [fields, businessNewsType]);

  const firstExtFields = extFields?.[0];
  const restExtFields = extFields?.slice(1);

  return (
    <div className="weekly-report-hidden-field">
      <div className="weekly-report-hidden-field-title">
        <div className="title">喜报字段隐藏</div>
        <span>
          下载时如需隐藏部分字段，点击后字段置灰不会在下载的图片中显示，再次点击可恢复显示（调整仅对下载的图片生效）
        </span>
      </div>
      <Wrap>
        {firstExtFields && (
          <HiddenFieldsWrap
            hiddenFields={extHiddenFields}
            fields={[firstExtFields]}
            handleClick={(item) => {
              onHiddenFieldChange(item.key);
            }}
          />
        )}
        <HiddenFieldsWrap {...props} handleClick={handleClick} />
        {restExtFields.length ? (
          <HiddenFieldsWrap
            hiddenFields={extHiddenFields}
            fields={restExtFields}
            handleClick={(item) => {
              onHiddenFieldChange(item.key);
            }}
          />
        ) : null}
      </Wrap>
    </div>
  );
}

const Card = styled.div`
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  border: 1px solid #f5222d;
  color: rgba(0, 0, 0, 0.85);
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  &.item-checked {
    border: 1px solid #000;
    background-color: rgba(0, 0, 0, 0.25);
  }
`;

export function HiddenFieldsWrap(props: {
  fields: any[];
  handleClick: (item) => void;
  hiddenFields: string[];
}) {
  const { fields, handleClick, hiddenFields } = props;
  const {
    setPeerRange,
    getPeerRange,
    dateRange,
    shopIdList,
    peerKeyMap,
    setPeerKeyMap,
    pid,
    allShopsSelected,
  } = useDataState();

  // 处理同行范围切换
  const handlePeerRangeChange = async (fieldKey: string, value: 'NORMAL' | 'CITY' | 'DISTRICT') => {
    setPeerRange(fieldKey, value);

    if (dateRange?.[0] && dateRange?.[1] && (shopIdList?.length || allShopsSelected)) {
      const keys = Array.from(peerKeyMap.keys()).filter((key) => {
        const item = peerKeyMap.get(key);
        // 筛选出属于当前分组且启用的字段
        return item?.parentKey === fieldKey && item?.enable;
      });
      await fetchPeerData(value, keys);
    }
  };
  useEffect(() => {
    emitter.on(EmitterEventMap.fetchPeerData, () => {
      // 获取所有启用的同行字段
      const enabledKeys = Array.from(peerKeyMap.keys()).filter((key) => {
        const item = peerKeyMap.get(key);
        return item?.enable;
      });
      fetchPeerData('DISTRICT', enabledKeys);
    });
    return () => {
      emitter.off(EmitterEventMap.fetchPeerData);
    };
  }, [peerKeyMap]);

  // 获取同行数据
  const fetchPeerData = async (
    range: 'NORMAL' | 'CITY' | 'DISTRICT',
    peerKeyList = Array.from(peerKeyMap.keys()),
  ) => {
    try {
      const requestParams: any = {
        startDate: dateRange[0],
        endDate: dateRange[1],
        range,
        peerKeyList: peerKeyList.join(','), // 使用可见字段列表
      };

      if (allShopsSelected) {
        // 如果选择了全部门店，传递pid
        requestParams.pid = pid;
      } else if (shopIdList?.length) {
        // 传递shopIdList
        requestParams.shopIdList = shopIdList;
      } else {
        return;
      }

      const result = await queryPeersData(requestParams);
      const peerValues = result?.applicationDataList?.[0]?.ruleDataList?.[0]?.values?.[0];

      const handlePeerValue = (key: string, obj: any) => {
        const peerValueKey = `${key}_vs_peers`;
        const diffKey = `${peerValueKey}_diff`;
        return {
          value: obj[key],
          peerValue: obj[peerValueKey],
          diff: obj[diffKey],
        };
      };
      peerKeyList.forEach((key) => {
        const item = handlePeerValue(key, peerValues);
        const existingItem = peerKeyMap.get(key);
        setPeerKeyMap(key, {
          enable: true,
          parentKey: existingItem?.parentKey,
          ...item,
        });
      });
    } catch (error) {
      // 接口调用失败时静默处理
    }
  };

  return (
    <>
      {fields.map((row, index) => {
        if (row.children) {
          const needsSelector = PEER_SELECTOR_DATA_TYPES.includes(row.label as any);

          return (
            <div key={index}>
              <div
                style={{
                  fontSize: 15,
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                }}
              >
                {row.label}
                {needsSelector && (
                  <>
                    <h4>展示同行均值对比</h4>
                    <RedSelect
                      defaultValue="NORMAL"
                      style={{ width: 140 }}
                      value={getPeerRange(row.label)}
                      options={[
                        { label: '周边5km范围', value: 'NORMAL' },
                        { label: '同区', value: 'DISTRICT' },
                        { label: '同城', value: 'CITY' },
                      ]}
                      onChange={async (value) => {
                        handlePeerRangeChange(row.label, value);
                      }}
                    />
                  </>
                )}
              </div>
              <HiddenFieldsWrap {...props} fields={row.children || row.expandChildrenData} />
            </div>
          );
        }
        const Render = row.render;
        if (Render) {
          return <Render key={index} />;
        }
        return (
          <>
            <Card
              className={classNames({
                'item-checked': hiddenFields.includes(row.key),
                item: true,
              })}
              onClick={() => handleClick(row)}
              key={row.key}
            >
              {row.title}
            </Card>
            {row.expandChildrenData?.length && (
              <HiddenFieldsWrap {...props} fields={row.expandChildrenData} />
            )}
          </>
        );
      })}
    </>
  );
}
